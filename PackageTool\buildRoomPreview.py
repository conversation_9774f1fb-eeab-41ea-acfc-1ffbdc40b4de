import sys
import uuid
import argparse
import buildProject
from ciTools import BuildProjectParam



if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='RoomPreview', description='')
    parser.add_argument('--projectBranch', type=str, required=True, help='xstudio分支')
    parser.add_argument('--appID', type=str, default='samba', help='应用的AppId')
    parser.add_argument('--releaseID', type=str, required=True, help='预发布releaseId')
    parser.add_argument('--executor', type=str, help='企微邮箱')
    parser.add_argument('--engineSearchDir', type=str, required=True, help='预编译引擎目录')
    parser.add_argument('--projectBuildUrl', type=str, help='jenkins链接')
    parser.add_argument('--packageToolDir', type=str, help='ci代码')
    parser.add_argument('--projectSourceDir', type=str, help='xstudio目录')
    parser.add_argument('--projectOutputDir', type=str, help='输出目录')
    args = parser.parse_args()
    
    buildParam = BuildProjectParam()
    buildParam.branch = args.projectBranch
    buildParam.engineMajorVersion = 5
    buildParam.engineBranch = 'feat/meta'
    buildParam.xsdkAppId = args.appID
    buildParam.xsdkReleaseId = args.releaseID
    buildParam.executor = args.executor

    # buildParam.forceResetLocalBranch = True
    # buildParam.embedCleanOldCache = True
    # buildParam.embedGitClean = False
    buildParam.bForceClearAllLocalCache = False
    buildParam.bUseSharedDDC = True
    
    buildParam.packageToolDir = args.packageToolDir
    buildParam.engineSearchDir = args.engineSearchDir
    buildParam.projectSourceDir = args.projectSourceDir
    buildParam.projectOutPutDir = args.projectOutputDir
    buildParam.projectBuildUrl = args.projectBuildUrl
    
    buildParam.bVRAndroidCi = True
    buildParam.bPrebuild = True
    buildParam.projectName = 'XVerseVR_Oculus'
    buildParam.targetPlatform = 'Android'
    buildParam.gameConfigurations = 'Development'
    buildParam.cookflavor = 'ASTC'
    buildParam.xsdkEnv = 'sit'

    buildParam.bCustomPak = True
    buildParam.bNeedApk = True
    buildParam.bNeedMainPak = True
    buildParam.bNeedVideo = True
    buildParam.bNeedLowmodelPak = True
    buildParam.panoVideoDstDir = f"C:\\work\\xverse\\PanoVideo\\{str(uuid.uuid1())}"
    buildParam.locateType = '锚点定位'
    buildParam.bPreRelease = True

    buildParam.clearDataWhenBuildFail = True
    buildParam.removeDataAfterBuild = True

    buildResult = buildProject.buildProjectMain(buildParam)
    sys.exit(0)
    



