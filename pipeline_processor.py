#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流水线处理器 - 通过HTTP接口调用完成应用发布流水线
"""

import httpx
import json
import time
import logging
from typing import Optional, Dict, Any
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PipelineProcessor:
    """流水线处理器类"""

    def __init__(self, base_url: str = "https://console-api.xverse.cn", timeout: int = 30):
        """
        初始化流水线处理器

        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.client = httpx.Client(
            timeout=timeout,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'PipelineProcessor/1.0'
            }
        )

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，确保客户端正确关闭"""
        self.close()
        return False

    def close(self):
        """关闭HTTP客户端"""
        if hasattr(self, 'client'):
            self.client.close()

    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.client.headers.update({'Authorization': f'Bearer {token}'})

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """
        发送HTTP请求的通用方法

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            响应的JSON数据

        Raises:
            httpx.HTTPError: 请求失败时抛出异常
        """
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        try:
            logger.info(f"发送 {method} 请求到: {url}")
            response = self.client.request(
                method=method,
                url=url,
                **kwargs
            )
            response.raise_for_status()

            result = response.json()
            logger.info(f"请求成功，响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result

        except httpx.HTTPError as e:
            logger.error(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    logger.error(f"错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
                except Exception:
                    logger.error(f"错误响应: {e.response.text}")
            raise
    
    def process_pipeline(self, app_id: str) -> str:
        """
        处理完整的流水线任务

        Args:
            app_id: 应用ID

        Returns:
            release_id: 发布ID
        """
        logger.info(f"开始处理应用 {app_id} 的流水线任务")

        try:
            # 步骤1: 获取分支信息
            branch_info = self._get_app_branches(app_id)
            logger.info(f"获取到分支信息: {branch_info}")

            # 步骤2: 创建预发布任务
            pre_release_id = self._create_pre_release(app_id, branch_info)
            logger.info(f"创建预发布任务，pre_release_id: {pre_release_id}")

            # 步骤3: 获取标签绑定信息
            tag_bind_info = self._get_tag_bind_list(app_id)
            logger.info(f"获取标签绑定信息: {tag_bind_info}")

            # 步骤4: 获取bundle列表
            bundle_info = self._get_bundle_list(app_id, tag_bind_info)
            logger.info(f"获取bundle列表信息: {bundle_info}")

            # 步骤5: 获取最新releaseId和psoInfo
            latest_release_info = self._get_latest_release_and_pso_info(app_id)
            logger.info(f"获取最新发布信息和psoInfo: {latest_release_info}")

            # 步骤6: 创建正式发布
            final_release_id = self._create_native_release(
                app_id,
                pre_release_id,
                tag_bind_info,
                bundle_info,
                latest_release_info
            )
            logger.info(f"创建正式发布完成，final_release_id: {final_release_id}")

            logger.info(f"流水线任务完成，最终 release_id: {final_release_id}")
            return final_release_id

        except Exception as e:
            logger.error(f"流水线处理失败: {e}")
            raise
    
    def _get_app_branches(self, app_id: str, release_type: str = "Normal") -> Dict[str, Any]:
        """
        获取应用的分支信息

        Args:
            app_id: 应用ID
            release_type: 发布类型，默认为"Normal"

        Returns:
            包含roomId、skinId、branchId信息的字典
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/branches'
        data = {'type': release_type}

        response = self._make_request('POST', endpoint, json=data)

        # 解析响应，提取roomId、skinId、branchId
        branch_info = self._parse_branch_response(response)
        return branch_info

    def _parse_branch_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析分支响应，提取roomId、skinId、branchId
        如果一个skin下有多个branch，默认取第一个

        Args:
            response: API响应数据

        Returns:
            包含roomId、skinId、branchId的字典
        """
        result = {
            'rooms': []
        }

        # 假设响应结构包含rooms数组
        if 'data' in response and 'rooms' in response['data']:
            for room in response['data']['rooms']:
                room_id = room.get('roomId')
                room_info = {
                    'roomId': room_id,
                    'skins': []
                }

                if 'skins' in room:
                    for skin in room['skins']:
                        skin_id = skin.get('skinId')
                        skin_info = {
                            'skinId': skin_id,
                            'branches': []
                        }

                        if 'branches' in skin and skin['branches']:
                            # 如果有多个branch，取第一个
                            first_branch = skin['branches'][0]
                            branch_id = first_branch.get('branchId')
                            skin_info['selectedBranchId'] = branch_id
                            skin_info['branches'] = skin['branches']

                        room_info['skins'].append(skin_info)

                result['rooms'].append(room_info)

        logger.info(f"解析得到的分支信息: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result

    def _create_pre_release(self, app_id: str, branch_info: Dict[str, Any]) -> str:
        """
        创建预发布任务

        Args:
            app_id: 应用ID
            branch_info: 从步骤1获取的分支信息

        Returns:
            pre_release_id: 预发布ID
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/pre_release/native'

        # 构建branchList
        branch_list = self._build_branch_list(branch_info)

        # 构建请求体
        data = {
            "appId": app_id,
            "name": "CIDailyBuild",
            "desc": "CIDailyBuild",
            "nativeAsset": {
                "ueVersion": "5_2_1"
            },
            "object": {
                "branchList": branch_list
            },
            "platformList": [
                "SOURCE",
                "ANDROID"
            ],
            "isSkipCook": True
        }

        logger.info(f"创建预发布任务请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

        response = self._make_request('POST', endpoint, json=data)

        # 从响应中提取pre_release_id
        pre_release_id = self._extract_pre_release_id(response)
        return pre_release_id

    def _build_branch_list(self, branch_info: Dict[str, Any]) -> list:
        """
        根据分支信息构建branchList

        Args:
            branch_info: 分支信息

        Returns:
            branchList: 分支列表
        """
        branch_list = []

        if 'rooms' in branch_info:
            for room in branch_info['rooms']:
                room_id = room.get('roomId')

                if 'skins' in room:
                    for skin in room['skins']:
                        skin_id = skin.get('skinId')
                        selected_branch_id = skin.get('selectedBranchId')

                        if room_id and skin_id and selected_branch_id:
                            branch_item = {
                                "roomId": str(room_id),
                                "skinId": str(skin_id),
                                "branchId": str(selected_branch_id)
                            }
                            branch_list.append(branch_item)

        logger.info(f"构建的branchList: {json.dumps(branch_list, ensure_ascii=False, indent=2)}")
        return branch_list

    def _extract_pre_release_id(self, response: Dict[Any, Any]) -> str:
        """
        从响应中提取pre_release_id

        Args:
            response: API响应

        Returns:
            pre_release_id: 预发布ID
        """
        # 根据实际API响应结构提取pre_release_id
        # 常见的可能字段名: id, preReleaseId, pre_release_id, releaseId等
        possible_fields = ['id', 'preReleaseId', 'pre_release_id', 'releaseId', 'data']

        for field in possible_fields:
            if field in response:
                if field == 'data' and isinstance(response[field], dict):
                    # 如果是data字段，继续在data内部查找
                    for sub_field in possible_fields[:-1]:  # 排除data字段本身
                        if sub_field in response[field]:
                            pre_release_id = str(response[field][sub_field])
                            logger.info(f"从响应的data.{sub_field}字段提取到pre_release_id: {pre_release_id}")
                            return pre_release_id
                else:
                    pre_release_id = str(response[field])
                    logger.info(f"从响应的{field}字段提取到pre_release_id: {pre_release_id}")
                    return pre_release_id

        # 如果没有找到，记录完整响应并抛出异常
        logger.error(f"无法从响应中提取pre_release_id，完整响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
        raise ValueError("无法从API响应中提取pre_release_id")

    def _get_tag_bind_list(self, app_id: str, ue_version: str = "5_2_1", store_tag_id: str = "common") -> Dict[str, Any]:
        """
        获取标签绑定列表

        Args:
            app_id: 应用ID
            ue_version: UE版本，默认为"5_2_1"
            store_tag_id: 存储标签ID，默认为"common"

        Returns:
            包含pathId和tagList的字典
        """
        endpoint = f'/release/console/asset_tag_mgt/v1/apps/{app_id}/get_tag_bind_list'
        params = {
            'ueVersion': ue_version,
            'storeTagId': store_tag_id
        }

        logger.info(f"获取标签绑定列表，参数: {params}")

        response = self._make_request('GET', endpoint, params=params)

        # 解析响应，提取pathId和tagList
        tag_bind_info = self._parse_tag_bind_response(response)
        return tag_bind_info

    def _parse_tag_bind_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析标签绑定响应，提取pathId和tagList

        Args:
            response: API响应数据

        Returns:
            包含pathId和tagList的字典
        """
        result = {
            'pathId': None,
            'tagList': []
        }

        # 尝试从不同可能的字段中提取pathId
        path_id_fields = ['pathId', 'path_id', 'id']
        tag_list_fields = ['tagList', 'tag_list', 'tags']

        # 检查是否有data字段包装
        data = response.get('data', response)

        # 提取pathId
        for field in path_id_fields:
            if field in data and data[field] is not None:
                result['pathId'] = str(data[field])
                logger.info(f"从{field}字段提取到pathId: {result['pathId']}")
                break

        # 提取tagList
        for field in tag_list_fields:
            if field in data and isinstance(data[field], list):
                result['tagList'] = data[field]
                logger.info(f"从{field}字段提取到tagList，包含{len(result['tagList'])}个标签")
                break

        # 如果在根级别没找到，尝试在嵌套结构中查找
        if result['pathId'] is None or not result['tagList']:
            # 检查是否有其他可能的嵌套结构
            for key, value in data.items():
                if isinstance(value, dict):
                    # 在嵌套对象中查找pathId
                    if result['pathId'] is None:
                        for field in path_id_fields:
                            if field in value and value[field] is not None:
                                result['pathId'] = str(value[field])
                                logger.info(f"从{key}.{field}字段提取到pathId: {result['pathId']}")
                                break

                    # 在嵌套对象中查找tagList
                    if not result['tagList']:
                        for field in tag_list_fields:
                            if field in value and isinstance(value[field], list):
                                result['tagList'] = value[field]
                                logger.info(f"从{key}.{field}字段提取到tagList，包含{len(result['tagList'])}个标签")
                                break

        logger.info(f"解析得到的标签绑定信息: {json.dumps(result, ensure_ascii=False, indent=2)}")

        # 验证必要字段
        if result['pathId'] is None:
            logger.warning("未能从响应中提取到pathId")
        if not result['tagList']:
            logger.warning("未能从响应中提取到tagList或tagList为空")

        return result

    def _get_bundle_list(self, app_id: str, tag_bind_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取bundle列表

        Args:
            app_id: 应用ID
            tag_bind_info: 从步骤3获取的标签绑定信息

        Returns:
            包含bundleList信息的字典
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/bundle_list'

        # 构建tagList查询参数
        tag_list = tag_bind_info.get('tagList', [])
        if not tag_list:
            logger.warning("tagList为空，无法获取bundle列表")
            return {'bundleList': []}

        # 构建查询参数，每个tag作为一个tagList参数
        params = []
        for tag in tag_list:
            # tag可能是字符串或字典，需要处理不同格式
            tag_value = self._extract_tag_value(tag)
            if tag_value:
                params.append(('tagList', tag_value))

        if not params:
            logger.warning("没有有效的tagList参数，无法获取bundle列表")
            return {'bundleList': []}

        logger.info(f"获取bundle列表，tagList参数: {[param[1] for param in params]}")

        # 使用httpx发送带有重复参数名的GET请求
        response = self._make_request_with_repeated_params('GET', endpoint, params)

        # 解析响应，提取bundleList
        bundle_info = self._parse_bundle_list_response(response)
        return bundle_info

    def _extract_tag_value(self, tag: Any) -> str:
        """
        从tag对象中提取标签值

        Args:
            tag: 标签对象，可能是字符串或字典

        Returns:
            标签值字符串
        """
        if isinstance(tag, str):
            return tag
        elif isinstance(tag, dict):
            # 尝试从常见字段中提取标签值
            possible_fields = ['name', 'tagName', 'tag_name', 'value', 'label', 'title']
            for field in possible_fields:
                if field in tag and tag[field]:
                    return str(tag[field])
            # 如果没有找到，返回整个字典的字符串表示
            return str(tag)
        else:
            return str(tag)

    def _make_request_with_repeated_params(self, method: str, endpoint: str, params: list) -> Dict[Any, Any]:
        """
        发送带有重复参数名的HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            params: 参数列表，格式为[(key, value), (key, value), ...]

        Returns:
            响应的JSON数据
        """
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        # 手动构建查询字符串以支持重复参数名
        from urllib.parse import urlencode
        query_string = urlencode(params)
        full_url = f"{url}?{query_string}"

        try:
            logger.info(f"发送 {method} 请求到: {full_url}")
            response = self.client.request(method=method, url=full_url)
            response.raise_for_status()

            result = response.json()
            logger.info(f"请求成功，响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result

        except httpx.HTTPError as e:
            logger.error(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    logger.error(f"错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
                except Exception:
                    logger.error(f"错误响应: {e.response.text}")
            raise

    def _parse_bundle_list_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析bundle列表响应，提取bundleList中的id

        Args:
            response: API响应数据

        Returns:
            包含bundleList和提取的id列表的字典
        """
        result = {
            'bundleList': [],
            'bundleIds': []
        }

        # 检查是否有data字段包装
        data = response.get('data', response)

        # 尝试从不同可能的字段中提取bundleList
        bundle_list_fields = ['bundleList', 'bundle_list', 'bundles', 'list']

        for field in bundle_list_fields:
            if field in data and isinstance(data[field], list):
                result['bundleList'] = data[field]
                logger.info(f"从{field}字段提取到bundleList，包含{len(result['bundleList'])}个bundle")
                break

        # 从bundleList中提取id
        for bundle in result['bundleList']:
            if isinstance(bundle, dict):
                # 尝试从常见字段中提取id
                id_fields = ['id', 'bundleId', 'bundle_id']
                for id_field in id_fields:
                    if id_field in bundle and bundle[id_field] is not None:
                        bundle_id = str(bundle[id_field])
                        result['bundleIds'].append(bundle_id)
                        logger.info(f"从bundle的{id_field}字段提取到id: {bundle_id}")
                        break

        logger.info(f"解析得到的bundle信息: bundleIds={result['bundleIds']}")

        if not result['bundleList']:
            logger.warning("未能从响应中提取到bundleList或bundleList为空")
        if not result['bundleIds']:
            logger.warning("未能从bundleList中提取到任何id")

        return result

    def _get_latest_release_and_pso_info(self, app_id: str) -> Dict[str, Any]:
        """
        获取最新的releaseId和psoInfo

        Args:
            app_id: 应用ID

        Returns:
            包含releaseId和psoInfo的字典
        """
        # 步骤5.1: 获取最新的releaseId
        latest_release_id = self._get_latest_release_id(app_id)

        # 步骤5.2: 获取psoInfo
        pso_info = self._get_pso_info(app_id, latest_release_id)

        return {
            'releaseId': latest_release_id,
            'psoInfo': pso_info
        }

    def _get_latest_release_id(self, app_id: str) -> str:
        """
        获取最新的releaseId

        Args:
            app_id: 应用ID

        Returns:
            最新的releaseId
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/page_read'

        # 构建请求体
        data = {
            "page": 0,
            "pageSize": 1,
            "tagList": [],
            "envList": [],
            "statusList": [],
            "creatorList": [],
            "typeList": [],
            "filter": {
                "key": "ID",
                "value": ""
            },
            "classList": [
                "NATIVE_RELEASE"
            ]
        }

        logger.info(f"获取最新releaseId，请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

        response = self._make_request('POST', endpoint, json=data)

        # 解析响应，提取最新的releaseId
        latest_release_id = self._extract_latest_release_id(response)
        return latest_release_id

    def _extract_latest_release_id(self, response: Dict[Any, Any]) -> str:
        """
        从page_read响应中提取最新的releaseId

        Args:
            response: API响应数据

        Returns:
            最新的releaseId
        """
        # 检查是否有data字段包装
        data = response.get('data', response)

        # 尝试从不同可能的字段中提取列表数据
        list_fields = ['list', 'items', 'records', 'data', 'releases']

        release_list = []
        for field in list_fields:
            if field in data and isinstance(data[field], list):
                release_list = data[field]
                logger.info(f"从{field}字段提取到发布列表，包含{len(release_list)}个发布")
                break

        if not release_list:
            logger.error(f"未能从响应中提取到发布列表，完整响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
            raise ValueError("无法从API响应中提取发布列表")

        # 获取第一个（最新的）发布记录
        if len(release_list) == 0:
            raise ValueError("发布列表为空，没有可用的发布记录")

        latest_release = release_list[0]

        # 从发布记录中提取releaseId
        id_fields = ['id', 'releaseId', 'release_id']
        for field in id_fields:
            if field in latest_release and latest_release[field] is not None:
                release_id = str(latest_release[field])
                logger.info(f"从最新发布记录的{field}字段提取到releaseId: {release_id}")
                return release_id

        logger.error(f"无法从最新发布记录中提取releaseId，发布记录: {json.dumps(latest_release, ensure_ascii=False, indent=2)}")
        raise ValueError("无法从最新发布记录中提取releaseId")

    def _get_pso_info(self, app_id: str, release_id: str, platform: str = "SOURCE") -> Dict[Any, Any]:
        """
        获取psoInfo

        Args:
            app_id: 应用ID
            release_id: 发布ID
            platform: 平台，默认为"SOURCE"

        Returns:
            psoInfo数据
        """
        # 构建config.json的URL
        config_url = f"https://static.xverse.cn/console/config/{app_id}/{release_id}/{platform}/config.json"

        try:
            logger.info(f"获取psoInfo，URL: {config_url}")

            # 使用GET请求获取config.json
            response = self.client.get(config_url)
            response.raise_for_status()

            config_data = response.json()
            logger.info("成功获取config.json")

            # 从config.json中提取psoInfo
            pso_info = self._extract_pso_info(config_data)
            return pso_info

        except httpx.HTTPError as e:
            logger.error(f"获取config.json失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"错误响应状态码: {e.response.status_code}")
                logger.error(f"错误响应内容: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"解析config.json失败: {e}")
            raise

    def _extract_pso_info(self, config_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """
        从config.json中提取psoInfo

        Args:
            config_data: config.json的数据

        Returns:
            psoInfo数据
        """
        # 尝试从不同可能的字段中提取psoInfo
        pso_info_fields = ['psoInfo', 'pso_info', 'pso', 'PSO']

        for field in pso_info_fields:
            if field in config_data:
                pso_info = config_data[field]
                logger.info(f"从config.json的{field}字段提取到psoInfo")
                return pso_info

        # 如果没有直接找到psoInfo字段，检查是否整个config就是psoInfo
        if isinstance(config_data, dict) and config_data:
            logger.info("未找到明确的psoInfo字段，将整个config作为psoInfo")
            return config_data

        logger.warning("未能从config.json中提取到psoInfo")
        return {}

    def _create_native_release(
        self,
        app_id: str,
        pre_release_id: str,
        tag_bind_info: Dict[str, Any],
        bundle_info: Dict[str, Any],
        latest_release_info: Dict[str, Any]
    ) -> str:
        """
        创建正式的native发布

        Args:
            app_id: 应用ID
            pre_release_id: 预发布ID
            tag_bind_info: 标签绑定信息
            bundle_info: bundle信息
            latest_release_info: 最新发布信息

        Returns:
            最终的releaseId
        """
        endpoint = f'/release/console/release_mgt/v2/apps/{app_id}/release/native'

        # 构建请求体
        data = self._build_native_release_data(
            app_id,
            pre_release_id,
            tag_bind_info,
            bundle_info,
            latest_release_info
        )

        logger.info(f"创建正式发布，请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

        response = self._make_request('POST', endpoint, json=data)

        # 从响应中提取最终的releaseId
        final_release_id = self._extract_final_release_id(response)
        return final_release_id

    def _build_native_release_data(
        self,
        app_id: str,
        pre_release_id: str,
        tag_bind_info: Dict[str, Any],
        bundle_info: Dict[str, Any],
        latest_release_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        构建native发布的请求体

        Args:
            app_id: 应用ID
            pre_release_id: 预发布ID
            tag_bind_info: 标签绑定信息
            bundle_info: bundle信息
            latest_release_info: 最新发布信息

        Returns:
            请求体数据
        """


        # 构建tagPathList
        tag_path_list = self._build_tag_path_list(tag_bind_info)

        # 构建bundleList
        bundle_list = bundle_info.get('bundleIds', [])

        # 获取psoInfo
        pso_info = latest_release_info.get('psoInfo', {})

        # 生成当前时间戳作为globalVersion
        global_version = str(int(time.time()))

        # 构建请求体
        data = {
            "appId": app_id,
            "name": "CIDailyBuild",
            "desc": "CIDailyBuild",
            "releaseId": pre_release_id,
            "nativeAsset": {
                "globalVersion": global_version,
                "tagPathList": tag_path_list,
                "extra": "",
                "ueVersion": "5_2_1",
                "assetInfoList": [],
                "lockedAssetInfoList": [],
                "psoInfo": pso_info,
                "storeTagId": "common"
            },
            "platformList": [
                "SOURCE",
                "ANDROID"
            ],
            "isSkipCook": True,
            "bundleList": bundle_list
        }

        return data

    def _build_tag_path_list(self, tag_bind_info: Dict[str, Any]) -> list:
        """
        构建tagPathList

        Args:
            tag_bind_info: 标签绑定信息

        Returns:
            tagPathList列表
        """
        tag_path_list = []

        # 从tag_bind_info中获取pathId
        path_id = tag_bind_info.get('pathId')

        if path_id:
            # 如果pathId是字符串，直接添加
            if isinstance(path_id, str):
                tag_path_list.append(path_id)
            # 如果pathId是列表，添加所有元素
            elif isinstance(path_id, list):
                tag_path_list.extend([str(p) for p in path_id if p])
            else:
                tag_path_list.append(str(path_id))

        # 如果没有pathId或为空，使用默认路径
        if not tag_path_list:
            logger.warning("未找到pathId，使用默认tagPathList")
            tag_path_list = [
                "/Game/XTD/Maps/XDTEntry",
                "/Game/XTD/Rooms/StartRoom/BP_StartRoom",
                "/Game/XTD/Rooms/Level1/BP_Level1"
            ]

        logger.info(f"构建的tagPathList: {tag_path_list}")
        return tag_path_list

    def _extract_final_release_id(self, response: Dict[Any, Any]) -> str:
        """
        从最终发布响应中提取releaseId

        Args:
            response: API响应

        Returns:
            最终的releaseId
        """
        # 根据实际API响应结构提取releaseId
        possible_fields = ['id', 'releaseId', 'release_id', 'data']

        for field in possible_fields:
            if field in response:
                if field == 'data' and isinstance(response[field], dict):
                    # 如果是data字段，继续在data内部查找
                    for sub_field in possible_fields[:-1]:  # 排除data字段本身
                        if sub_field in response[field]:
                            final_release_id = str(response[field][sub_field])
                            logger.info(f"从响应的data.{sub_field}字段提取到final_release_id: {final_release_id}")
                            return final_release_id
                else:
                    final_release_id = str(response[field])
                    logger.info(f"从响应的{field}字段提取到final_release_id: {final_release_id}")
                    return final_release_id

        # 如果没有找到，记录完整响应并抛出异常
        logger.error(f"无法从响应中提取final_release_id，完整响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
        raise ValueError("无法从API响应中提取final_release_id")




def pipeline_task(app_id: str, auth_token: Optional[str] = None, base_url: str = "https://console-api.xverse.cn") -> str:
    """
    流水线任务主函数

    Args:
        app_id: 应用ID
        auth_token: 认证令牌（可选）
        base_url: API基础URL

    Returns:
        release_id: 发布ID
    """
    with PipelineProcessor(base_url=base_url) as processor:
        if auth_token:
            processor.set_auth_token(auth_token)

        return processor.process_pipeline(app_id)


if __name__ == "__main__":
    # 测试示例
    test_app_id = "11391"
    
    try:
        release_id = pipeline_task(test_app_id)
        print(f"流水线任务完成，release_id: {release_id}")
    except Exception as e:
        print(f"流水线任务失败: {e}")
