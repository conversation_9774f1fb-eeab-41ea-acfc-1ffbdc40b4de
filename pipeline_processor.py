#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流水线处理器 - 通过HTTP接口调用完成应用发布流水线
"""

import httpx
import json
import time
import logging
from typing import Optional, Dict, Any
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PipelineProcessor:
    """流水线处理器类"""

    def __init__(self, base_url: str = "https://console-api.xverse.cn", timeout: int = 30):
        """
        初始化流水线处理器

        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.client = httpx.Client(
            timeout=timeout,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'PipelineProcessor/1.0'
            }
        )

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，确保客户端正确关闭"""
        self.close()
        return False

    def close(self):
        """关闭HTTP客户端"""
        if hasattr(self, 'client'):
            self.client.close()

    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.client.headers.update({'Authorization': f'Bearer {token}'})

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """
        发送HTTP请求的通用方法

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            响应的JSON数据

        Raises:
            httpx.HTTPError: 请求失败时抛出异常
        """
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        try:
            logger.info(f"发送 {method} 请求到: {url}")
            response = self.client.request(
                method=method,
                url=url,
                **kwargs
            )
            response.raise_for_status()

            result = response.json()
            logger.info(f"请求成功，响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result

        except httpx.HTTPError as e:
            logger.error(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    logger.error(f"错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
                except Exception:
                    logger.error(f"错误响应: {e.response.text}")
            raise
    
    def process_pipeline(self, app_id: str) -> str:
        """
        处理完整的流水线任务

        Args:
            app_id: 应用ID

        Returns:
            release_id: 发布ID
        """
        logger.info(f"开始处理应用 {app_id} 的流水线任务")

        try:
            # 步骤1: 获取分支信息
            branch_info = self._get_app_branches(app_id)
            logger.info(f"获取到分支信息: {branch_info}")

            # 步骤2: 创建预发布任务
            pre_release_id = self._create_pre_release(app_id, branch_info)
            logger.info(f"创建预发布任务，pre_release_id: {pre_release_id}")

            # 步骤3: 获取标签绑定信息
            tag_bind_info = self._get_tag_bind_list(app_id)
            logger.info(f"获取标签绑定信息: {tag_bind_info}")

            # 步骤4: 启动构建流程
            build_result = self._start_build(app_id, pre_release_id)
            logger.info(f"启动构建流程: {build_result}")

            # 步骤5: 等待构建完成
            self._wait_for_build_completion(app_id, pre_release_id)

            # 步骤6: 执行发布
            publish_result = self._publish_release(app_id, pre_release_id)
            logger.info(f"发布完成: {publish_result}")

            logger.info(f"流水线任务完成，最终 release_id: {pre_release_id}")
            return pre_release_id

        except Exception as e:
            logger.error(f"流水线处理失败: {e}")
            raise
    
    def _get_app_branches(self, app_id: str, release_type: str = "Normal") -> Dict[str, Any]:
        """
        获取应用的分支信息

        Args:
            app_id: 应用ID
            release_type: 发布类型，默认为"Normal"

        Returns:
            包含roomId、skinId、branchId信息的字典
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/branches'
        data = {'type': release_type}

        response = self._make_request('POST', endpoint, json=data)

        # 解析响应，提取roomId、skinId、branchId
        branch_info = self._parse_branch_response(response)
        return branch_info

    def _parse_branch_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析分支响应，提取roomId、skinId、branchId
        如果一个skin下有多个branch，默认取第一个

        Args:
            response: API响应数据

        Returns:
            包含roomId、skinId、branchId的字典
        """
        result = {
            'rooms': []
        }

        # 假设响应结构包含rooms数组
        if 'data' in response and 'rooms' in response['data']:
            for room in response['data']['rooms']:
                room_id = room.get('roomId')
                room_info = {
                    'roomId': room_id,
                    'skins': []
                }

                if 'skins' in room:
                    for skin in room['skins']:
                        skin_id = skin.get('skinId')
                        skin_info = {
                            'skinId': skin_id,
                            'branches': []
                        }

                        if 'branches' in skin and skin['branches']:
                            # 如果有多个branch，取第一个
                            first_branch = skin['branches'][0]
                            branch_id = first_branch.get('branchId')
                            skin_info['selectedBranchId'] = branch_id
                            skin_info['branches'] = skin['branches']

                        room_info['skins'].append(skin_info)

                result['rooms'].append(room_info)

        logger.info(f"解析得到的分支信息: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result

    def _create_pre_release(self, app_id: str, branch_info: Dict[str, Any]) -> str:
        """
        创建预发布任务

        Args:
            app_id: 应用ID
            branch_info: 从步骤1获取的分支信息

        Returns:
            pre_release_id: 预发布ID
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/pre_release/native'

        # 构建branchList
        branch_list = self._build_branch_list(branch_info)

        # 构建请求体
        data = {
            "appId": app_id,
            "name": "CIDailyBuild",
            "desc": "CIDailyBuild",
            "nativeAsset": {
                "ueVersion": "5_2_1"
            },
            "object": {
                "branchList": branch_list
            },
            "platformList": [
                "SOURCE",
                "ANDROID"
            ],
            "isSkipCook": True
        }

        logger.info(f"创建预发布任务请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

        response = self._make_request('POST', endpoint, json=data)

        # 从响应中提取pre_release_id
        pre_release_id = self._extract_pre_release_id(response)
        return pre_release_id

    def _build_branch_list(self, branch_info: Dict[str, Any]) -> list:
        """
        根据分支信息构建branchList

        Args:
            branch_info: 分支信息

        Returns:
            branchList: 分支列表
        """
        branch_list = []

        if 'rooms' in branch_info:
            for room in branch_info['rooms']:
                room_id = room.get('roomId')

                if 'skins' in room:
                    for skin in room['skins']:
                        skin_id = skin.get('skinId')
                        selected_branch_id = skin.get('selectedBranchId')

                        if room_id and skin_id and selected_branch_id:
                            branch_item = {
                                "roomId": str(room_id),
                                "skinId": str(skin_id),
                                "branchId": str(selected_branch_id)
                            }
                            branch_list.append(branch_item)

        logger.info(f"构建的branchList: {json.dumps(branch_list, ensure_ascii=False, indent=2)}")
        return branch_list

    def _extract_pre_release_id(self, response: Dict[Any, Any]) -> str:
        """
        从响应中提取pre_release_id

        Args:
            response: API响应

        Returns:
            pre_release_id: 预发布ID
        """
        # 根据实际API响应结构提取pre_release_id
        # 常见的可能字段名: id, preReleaseId, pre_release_id, releaseId等
        possible_fields = ['id', 'preReleaseId', 'pre_release_id', 'releaseId', 'data']

        for field in possible_fields:
            if field in response:
                if field == 'data' and isinstance(response[field], dict):
                    # 如果是data字段，继续在data内部查找
                    for sub_field in possible_fields[:-1]:  # 排除data字段本身
                        if sub_field in response[field]:
                            pre_release_id = str(response[field][sub_field])
                            logger.info(f"从响应的data.{sub_field}字段提取到pre_release_id: {pre_release_id}")
                            return pre_release_id
                else:
                    pre_release_id = str(response[field])
                    logger.info(f"从响应的{field}字段提取到pre_release_id: {pre_release_id}")
                    return pre_release_id

        # 如果没有找到，记录完整响应并抛出异常
        logger.error(f"无法从响应中提取pre_release_id，完整响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
        raise ValueError("无法从API响应中提取pre_release_id")

    def _get_tag_bind_list(self, app_id: str, ue_version: str = "5_2_1", store_tag_id: str = "common") -> Dict[str, Any]:
        """
        获取标签绑定列表

        Args:
            app_id: 应用ID
            ue_version: UE版本，默认为"5_2_1"
            store_tag_id: 存储标签ID，默认为"common"

        Returns:
            包含pathId和tagList的字典
        """
        endpoint = f'/release/console/asset_tag_mgt/v1/apps/{app_id}/get_tag_bind_list'
        params = {
            'ueVersion': ue_version,
            'storeTagId': store_tag_id
        }

        logger.info(f"获取标签绑定列表，参数: {params}")

        response = self._make_request('GET', endpoint, params=params)

        # 解析响应，提取pathId和tagList
        tag_bind_info = self._parse_tag_bind_response(response)
        return tag_bind_info

    def _parse_tag_bind_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析标签绑定响应，提取pathId和tagList

        Args:
            response: API响应数据

        Returns:
            包含pathId和tagList的字典
        """
        result = {
            'pathId': None,
            'tagList': []
        }

        # 尝试从不同可能的字段中提取pathId
        path_id_fields = ['pathId', 'path_id', 'id']
        tag_list_fields = ['tagList', 'tag_list', 'tags']

        # 检查是否有data字段包装
        data = response.get('data', response)

        # 提取pathId
        for field in path_id_fields:
            if field in data and data[field] is not None:
                result['pathId'] = str(data[field])
                logger.info(f"从{field}字段提取到pathId: {result['pathId']}")
                break

        # 提取tagList
        for field in tag_list_fields:
            if field in data and isinstance(data[field], list):
                result['tagList'] = data[field]
                logger.info(f"从{field}字段提取到tagList，包含{len(result['tagList'])}个标签")
                break

        # 如果在根级别没找到，尝试在嵌套结构中查找
        if result['pathId'] is None or not result['tagList']:
            # 检查是否有其他可能的嵌套结构
            for key, value in data.items():
                if isinstance(value, dict):
                    # 在嵌套对象中查找pathId
                    if result['pathId'] is None:
                        for field in path_id_fields:
                            if field in value and value[field] is not None:
                                result['pathId'] = str(value[field])
                                logger.info(f"从{key}.{field}字段提取到pathId: {result['pathId']}")
                                break

                    # 在嵌套对象中查找tagList
                    if not result['tagList']:
                        for field in tag_list_fields:
                            if field in value and isinstance(value[field], list):
                                result['tagList'] = value[field]
                                logger.info(f"从{key}.{field}字段提取到tagList，包含{len(result['tagList'])}个标签")
                                break

        logger.info(f"解析得到的标签绑定信息: {json.dumps(result, ensure_ascii=False, indent=2)}")

        # 验证必要字段
        if result['pathId'] is None:
            logger.warning("未能从响应中提取到pathId")
        if not result['tagList']:
            logger.warning("未能从响应中提取到tagList或tagList为空")

        return result

    def _start_build(self, app_id: str, pre_release_id: str) -> Dict[Any, Any]:
        """启动构建流程"""
        # TODO: 根据实际API文档实现
        data = {'pre_release_id': pre_release_id}
        return self._make_request('POST', f'/api/apps/{app_id}/builds', json=data)

    def _wait_for_build_completion(self, app_id: str, pre_release_id: str, max_wait_time: int = 1800):
        """等待构建完成"""
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            # TODO: 根据实际API文档实现状态查询
            status = self._get_build_status(app_id, pre_release_id)

            if status.get('status') == 'completed':
                logger.info("构建完成")
                return
            elif status.get('status') == 'failed':
                raise Exception(f"构建失败: {status.get('error', '未知错误')}")

            logger.info(f"构建进行中，状态: {status.get('status', '未知')}")
            time.sleep(30)  # 等待30秒后重新检查

        raise Exception(f"构建超时，超过最大等待时间 {max_wait_time} 秒")

    def _get_build_status(self, app_id: str, pre_release_id: str) -> Dict[Any, Any]:
        """获取构建状态"""
        # TODO: 根据实际API文档实现
        return self._make_request('GET', f'/api/apps/{app_id}/builds/{pre_release_id}/status')

    def _publish_release(self, app_id: str, pre_release_id: str) -> Dict[Any, Any]:
        """执行发布"""
        # TODO: 根据实际API文档实现
        data = {'pre_release_id': pre_release_id}
        return self._make_request('POST', f'/api/apps/{app_id}/releases/{pre_release_id}/publish', json=data)


def pipeline_task(app_id: str, auth_token: Optional[str] = None, base_url: str = "https://console-api.xverse.cn") -> str:
    """
    流水线任务主函数

    Args:
        app_id: 应用ID
        auth_token: 认证令牌（可选）
        base_url: API基础URL

    Returns:
        release_id: 发布ID
    """
    with PipelineProcessor(base_url=base_url) as processor:
        if auth_token:
            processor.set_auth_token(auth_token)

        return processor.process_pipeline(app_id)


if __name__ == "__main__":
    # 测试示例
    test_app_id = "11391"
    
    try:
        release_id = pipeline_task(test_app_id)
        print(f"流水线任务完成，release_id: {release_id}")
    except Exception as e:
        print(f"流水线任务失败: {e}")
