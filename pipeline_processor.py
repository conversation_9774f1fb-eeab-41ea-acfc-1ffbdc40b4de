#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流水线处理器 - 通过HTTP接口调用完成应用发布流水线
"""

import httpx
import json
import time
import logging
from typing import Optional, Dict, Any
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PipelineProcessor:
    """流水线处理器类"""

    def __init__(self, base_url: str = "https://console-api.xverse.cn", timeout: int = 30):
        """
        初始化流水线处理器

        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.client = httpx.Client(
            timeout=timeout,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'PipelineProcessor/1.0'
            }
        )

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，确保客户端正确关闭"""
        self.close()
        return False

    def close(self):
        """关闭HTTP客户端"""
        if hasattr(self, 'client'):
            self.client.close()

    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.client.headers.update({'Authorization': f'Bearer {token}'})

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """
        发送HTTP请求的通用方法

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            响应的JSON数据

        Raises:
            httpx.HTTPError: 请求失败时抛出异常
        """
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        try:
            logger.info(f"发送 {method} 请求到: {url}")
            response = self.client.request(
                method=method,
                url=url,
                **kwargs
            )
            response.raise_for_status()

            result = response.json()
            logger.info(f"请求成功，响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result

        except httpx.HTTPError as e:
            logger.error(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    logger.error(f"错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
                except Exception:
                    logger.error(f"错误响应: {e.response.text}")
            raise
    
    def process_pipeline(self, app_id: str) -> str:
        """
        处理完整的流水线任务

        Args:
            app_id: 应用ID

        Returns:
            release_id: 发布ID
        """
        logger.info(f"开始处理应用 {app_id} 的流水线任务")

        try:
            # 步骤1: 获取分支信息
            branch_info = self._get_app_branches(app_id)
            logger.info(f"获取到分支信息: {branch_info}")

            # 步骤2: 创建发布任务
            release_id = self._create_release(app_id)
            logger.info(f"创建发布任务，release_id: {release_id}")

            # 步骤3: 启动构建流程
            build_result = self._start_build(app_id, release_id)
            logger.info(f"启动构建流程: {build_result}")

            # 步骤4: 等待构建完成
            self._wait_for_build_completion(app_id, release_id)

            # 步骤5: 执行发布
            publish_result = self._publish_release(app_id, release_id)
            logger.info(f"发布完成: {publish_result}")

            logger.info(f"流水线任务完成，最终 release_id: {release_id}")
            return release_id

        except Exception as e:
            logger.error(f"流水线处理失败: {e}")
            raise
    
    def _get_app_branches(self, app_id: str, release_type: str = "Normal") -> Dict[str, Any]:
        """
        获取应用的分支信息

        Args:
            app_id: 应用ID
            release_type: 发布类型，默认为"Normal"

        Returns:
            包含roomId、skinId、branchId信息的字典
        """
        endpoint = f'/release/console/release_mgt/v1/apps/{app_id}/branches'
        data = {'type': release_type}

        response = self._make_request('POST', endpoint, json=data)

        # 解析响应，提取roomId、skinId、branchId
        branch_info = self._parse_branch_response(response)
        return branch_info

    def _parse_branch_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析分支响应，提取roomId、skinId、branchId
        如果一个skin下有多个branch，默认取第一个

        Args:
            response: API响应数据

        Returns:
            包含roomId、skinId、branchId的字典
        """
        result = {
            'rooms': []
        }

        # 假设响应结构包含rooms数组
        if 'data' in response and 'rooms' in response['data']:
            for room in response['data']['rooms']:
                room_id = room.get('roomId')
                room_info = {
                    'roomId': room_id,
                    'skins': []
                }

                if 'skins' in room:
                    for skin in room['skins']:
                        skin_id = skin.get('skinId')
                        skin_info = {
                            'skinId': skin_id,
                            'branches': []
                        }

                        if 'branches' in skin and skin['branches']:
                            # 如果有多个branch，取第一个
                            first_branch = skin['branches'][0]
                            branch_id = first_branch.get('branchId')
                            skin_info['selectedBranchId'] = branch_id
                            skin_info['branches'] = skin['branches']

                        room_info['skins'].append(skin_info)

                result['rooms'].append(room_info)

        logger.info(f"解析得到的分支信息: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result

    def _create_release(self, app_id: str) -> str:
        """创建发布任务"""
        # TODO: 根据实际API文档实现
        data = {'app_id': app_id}
        result = self._make_request('POST', f'/api/apps/{app_id}/releases', json=data)
        return result.get('release_id', '')
    
    def _start_build(self, app_id: str, release_id: str) -> Dict[Any, Any]:
        """启动构建流程"""
        # TODO: 根据实际API文档实现
        data = {'release_id': release_id}
        return self._make_request('POST', f'/api/apps/{app_id}/builds', json=data)
    
    def _wait_for_build_completion(self, app_id: str, release_id: str, max_wait_time: int = 1800):
        """等待构建完成"""
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            # TODO: 根据实际API文档实现状态查询
            status = self._get_build_status(app_id, release_id)
            
            if status.get('status') == 'completed':
                logger.info("构建完成")
                return
            elif status.get('status') == 'failed':
                raise Exception(f"构建失败: {status.get('error', '未知错误')}")
            
            logger.info(f"构建进行中，状态: {status.get('status', '未知')}")
            time.sleep(30)  # 等待30秒后重新检查
        
        raise Exception(f"构建超时，超过最大等待时间 {max_wait_time} 秒")
    
    def _get_build_status(self, app_id: str, release_id: str) -> Dict[Any, Any]:
        """获取构建状态"""
        # TODO: 根据实际API文档实现
        return self._make_request('GET', f'/api/apps/{app_id}/builds/{release_id}/status')
    
    def _publish_release(self, app_id: str, release_id: str) -> Dict[Any, Any]:
        """执行发布"""
        # TODO: 根据实际API文档实现
        data = {'release_id': release_id}
        return self._make_request('POST', f'/api/apps/{app_id}/releases/{release_id}/publish', json=data)


def pipeline_task(app_id: str, auth_token: Optional[str] = None, base_url: str = "https://console.xverse.cn") -> str:
    """
    流水线任务主函数

    Args:
        app_id: 应用ID
        auth_token: 认证令牌（可选）
        base_url: API基础URL

    Returns:
        release_id: 发布ID
    """
    with PipelineProcessor(base_url=base_url) as processor:
        if auth_token:
            processor.set_auth_token(auth_token)

        return processor.process_pipeline(app_id)


if __name__ == "__main__":
    # 测试示例
    test_app_id = "11391"
    
    try:
        release_id = pipeline_task(test_app_id)
        print(f"流水线任务完成，release_id: {release_id}")
    except Exception as e:
        print(f"流水线任务失败: {e}")
