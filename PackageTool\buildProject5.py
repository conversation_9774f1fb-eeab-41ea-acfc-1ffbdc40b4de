#!/usr/bin/python
#-*- coding: utf-8
import sys
import json
import os
from pathlib import Path
import subprocess
import json
import codecs
import shutil
import io
import uuid

import ciTools
from ciTools import BuildProjectParam
import buildProject
sys.path.append(".")

logTag = "BuildProject5"

def printLog(msg):
    ciTools.printLogTag(logTag, msg)
    #print(msg)
    pass

if __name__ == "__main__":
    #sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    #sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    cwd = os.getcwd()
    printLog("cwd=" + cwd)
    BinPath = sys.argv[0]
    printLog("BinPath=" + BinPath)
    buildParam = BuildProjectParam()
    buildProject.readParams(sys.argv, buildParam)
    buildParam.engineMajorVersion = 5
    buildParam.createBuildProjectWithGen = True
    if buildParam.embedLocalTest == True:
        buildParam.projectName = "XCapture"
        buildParam.targetPlatform = "Win64"
        buildParam.notifyForTest = True
        buildParam.engineBranch = "xstudio-ue5.2"
        buildParam.branch = "dev-ue5"
        buildParam.embedGitClean = False
        buildParam.projectSourceDir = "C:\\work\\xverse\\XVerseStudio5.2"
        buildParam.projectOutPutDir = "C:\\work\\xverse\\Project5OutPut"
        buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\XCreatorProject"
        buildParam.engineSearchDir = "C:\\work\\xverse\\Engine5OutPut"
        #buildParam.engineDir = "C:\\work\\xverse\\UnrealEngine5.2\\UnrealEngine"
        buildParam.embedUploadSamba = False
        buildParam.embedSwitchBranch = False
        buildParam.forceResetLocalBranch = False
        buildParam.embedPullLatestCode = False
        buildParam.embedCleanOldCache = False
        buildParam.embedSymStore = False
        buildParam.embedRemoveSymbol = False
        buildParam.embedUploadSymbol = False
        buildParam.gameConfigurations = "Development"
        buildParam.createBuildProjectWithGen = True
        buildParam.embedProjectStage = True
        buildParam.packageWithLocalAsset = True
        buildParam.downloadAssetWithLocalCopy = True
    # pengzhu
    buildParam.bVRAndroidCi = True
    if not buildParam.bPrebuild:
        buildParam.bVRAndroidCiForEngine = True

    if buildParam.pakMode == 'obb':
        buildParam.bCustomPak = False
    else:
        buildParam.bCustomPak = True
    
    if buildParam.psoCollectMode == 'noCollect':
        buildParam.bCollectPso = False
    else:
        buildParam.bCollectPso = True
        if buildParam.psoCollectMode == 'collectByPsoWorld':
            buildParam.bGeneratePsoWorld = True
            buildParam.bCustomPak = False
            buildParam.bNeedApk = True

    if buildParam.bCustomPak:
        if buildParam.otaOption and buildParam.otaPath:
            buildParam.bOTA = True
            otaOptions = buildParam.otaOption.strip().split(',')
            otaPaths = buildParam.otaPath.strip().split(',')
            
            for idx, opt in enumerate(otaOptions):
                if opt == 'bNeedApk':
                    buildParam.bNeedApk = True
                    buildParam.apkCosPrefix = otaPaths[idx]
                elif opt == 'bNeedMainPak':
                    buildParam.bNeedMainPak = True
                    buildParam.pakCosPrefix = otaPaths[idx]
                elif opt == 'bNeedLowmodelPak':
                    buildParam.bNeedLowmodelPak = True
                    buildParam.lowmodelPakCosPrefix = otaPaths[idx]
                elif opt == 'bNeedVideos':
                    buildParam.bNeedVideo = True
                    buildParam.videoCosPrefix = otaPaths[idx]
                    buildParam.panoVideoDstDir = f"C:\\work\\xverse\\PanoVideo\\{str(uuid.uuid1())}"
            if not (buildParam.bNeedApk or buildParam.bNeedMainPak):
                if buildParam.bNeedLowmodelPak:
                    buildParam.bOnlyLowmodelPak = True
                else:
                    # 不需要再判断是否需要video，如果都不勾选那就是空            
                    buildParam.bOnlyVideo = True
                    buildParam.backgroundUpload = False
        else:
            if buildParam.pakMode == 'customPakAll' or buildParam.pakMode == 'custompakAll':
                buildParam.bNeedApk = True
                buildParam.bNeedMainPak = True
                buildParam.bNeedLowmodelPak = True
            elif buildParam.pakMode == 'apk':
                buildParam.bNeedApk = True
                buildParam.bCollectPso = False
            elif buildParam.pakMode == 'mainPak+lowmodelPak':
                buildParam.bNeedMainPak = True
                buildParam.bNeedLowmodelPak = True
            elif buildParam.pakMode == 'mainPak':
                buildParam.bNeedMainPak = True
            elif buildParam.pakMode == 'lowmodelPak':
                buildParam.bNeedLowmodelPak = True
                buildParam.bOnlyLowmodelPak = True
                buildParam.bCollectPso = False
    
            if buildParam.videoName and buildParam.storeName and not buildParam.bCollectPso and not buildParam.bPreRelease:
                buildParam.bOTA = True
                if buildParam.pakMode in ['customPakAll', 'mainPak+lowmodelPak', 'mainPak']:
                    buildParam.panoVideoDstDir = f"C:\\work\\xverse\\PanoVideo\\{str(uuid.uuid1())}"
        if buildParam.bNeedApk and buildParam.bNeedLowmodelPak and buildParam.bNeedMainPak and buildParam.bNeedVideo:
            buildParam.bRelease = True
    # buildParam.bForceUploadOTA = True
    if buildParam.deviceType == 'pico':
        buildParam.createBuildProjectWithGen = False
    buildParam.clearDataWhenBuildFail = True
    buildParam.removeDataAfterBuild = True
    # if buildParam.bVRAndroidCi == True:
        # buildParam.targetPlatform = "Android"
        # buildParam.embedGitClean = True
        # buildParam.gameConfigurations = "Development"

        # buildParam.cookflavor = "ASTC"
        # buildParam.xsdkAppId = "11196"
        # buildParam.xsdkReleaseId = "2408122132_f0f6d7"
        # buildParam.xsdkVersion = "0.1000.0"
        # buildParam.xsdkEnv = "sit"

        # buildParam.projectSourceDir = "D:\\work\\xverse\\XVerseStudio"
        # buildParam.projectOutPutDir = "D:\\work\\xverse\\Project5OutPut"
        # buildParam.projectName = "XVerseVR_Oculus"
        # buildParam.branch = "feat/UnderTheSeaMeta"
        # buildParam.commitId = ""
        
        # buildParam.engineDir = "D:\\work\\xverse\\UnrealEngine"
        # buildParam.engineBranch = "feat/meta"
        # buildParam.engineCommitId = ""
        
    buildResult = buildProject.buildProjectMain(buildParam)
    printLog(f"buildResult={buildResult}")
    if buildResult:
        sys.exit(0)
    else:
        sys.exit(1)